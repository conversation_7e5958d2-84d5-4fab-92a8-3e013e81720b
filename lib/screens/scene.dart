import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class Scene {
  final String id;
  final String name;
  final Map<String, dynamic> entities;
  final String icon;
  final Map<String, dynamic> metadata;

  Scene({
    required this.id,
    required this.name,
    required this.entities,
    required this.icon,
    required this.metadata,
  });

  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      entities: json['entities'] ?? {},
      icon: json['icon'] ?? 'mdi:account',
      metadata: json['metadata'] ?? {},
    );
  }
}

class ApiService {
  static const String baseUrl =
      'http://homeassistant.local:8123/api/config/scene/config';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  static Future<bool> createScene({
    required String name,
    required String entityId,
    required int brightness,
    required List<int> rgbColor,
    required String state,
  }) async {
    try {
      final sceneId = DateTime.now().millisecondsSinceEpoch.toString();
      final url = '$baseUrl/$sceneId';

      final body = {
        "name": name,
        "entities": {
          entityId: {
            "min_color_temp_kelvin": 2127,
            "max_color_temp_kelvin": 6535,
            "min_mireds": 153,
            "max_mireds": 470,
            "supported_color_modes": ["color_temp", "hs", "xy"],
            "color_mode": "color_temp",
            "brightness": brightness,
            "color_temp_kelvin": 2127,
            "color_temp": 470,
            "hs_color": [30.069, 88.053],
            "rgb_color": rgbColor,
            "xy_color": [0.586, 0.387],
            "friendly_name":
                entityId.split('.').last.replaceAll('_', ' ').toUpperCase(),
            "supported_features": 32,
            "state": state,
          },
        },
        "icon": "mdi:account",
        "metadata": {},
      };

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(body),
      );

      return response.statusCode == 200;
    } catch (e) {
      log('Error creating scene: $e');
      return false;
    }
  }

  static Future<Scene?> getScene(String sceneId) async {
    try {
      final url = '$baseUrl/$sceneId';
      final response = await http.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Scene.fromJson(data);
      }
      return null;
    } catch (e) {
      log('Error fetching scene: $e');
      return null;
    }
  }

  static Future<List<Scene>> getAllScenes() async {
    // Since Home Assistant doesn't provide a list all scenes endpoint in your example,
    // we'll simulate it by trying to fetch known scene IDs
    // In a real app, you might want to store scene IDs locally or use a different API endpoint
    List<Scene> scenes = [];

    // This is a simulation - in reality you'd get this list from HA or store locally
    List<String> knownSceneIds = [
      '1750054948098',
    ]; // Add your known scene IDs here

    for (String id in knownSceneIds) {
      Scene? scene = await getScene(id);
      if (scene != null) {
        scenes.add(scene);
      }
    }

    return scenes;
  }
}

class SceneListPage extends StatefulWidget {
  const SceneListPage({super.key});

  @override
  SceneListPageState createState() => SceneListPageState();
}

class SceneListPageState extends State<SceneListPage> {
  List<Scene> scenes = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadScenes();
  }

  Future<void> loadScenes() async {
    setState(() => isLoading = true);
    scenes = await ApiService.getAllScenes();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Home Assistant Scenes'),
        backgroundColor: Colors.blue,
      ),
      body:
          isLoading
              ? Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: loadScenes,
                child:
                    scenes.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.lightbulb_outline,
                                size: 64,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'No scenes found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Pull to refresh or create a new scene',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: scenes.length,
                          itemBuilder: (context, index) {
                            final scene = scenes[index];
                            return Card(
                              margin: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.blue,
                                  child: Icon(
                                    Icons.lightbulb,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(
                                  scene.name,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Text(
                                  '${scene.entities.length} entities',
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                                trailing: Icon(Icons.arrow_forward_ios),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              SceneDetailPage(scene: scene),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => CreateScenePage()),
          );
          if (result == true) {
            loadScenes();
          }
        },
        backgroundColor: Colors.blue,
        child: Icon(Icons.add),
      ),
    );
  }
}

class CreateScenePage extends StatefulWidget {
  const CreateScenePage({super.key});

  @override
  CreateScenePageState createState() => CreateScenePageState();
}

class CreateScenePageState extends State<CreateScenePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _entityIdController = TextEditingController();
  final _brightnessController = TextEditingController();
  final _redController = TextEditingController();
  final _greenController = TextEditingController();
  final _blueController = TextEditingController();
  String _selectedState = 'on';
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    // Set default values
    _entityIdController.text = 'light.essentials_lightstrip';
    _brightnessController.text = '112';
    _redController.text = '255';
    _greenController.text = '143';
    _blueController.text = '30';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _entityIdController.dispose();
    _brightnessController.dispose();
    _redController.dispose();
    _greenController.dispose();
    _blueController.dispose();
    super.dispose();
  }

  Future<void> _createScene() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      final rgbColor = [
        int.parse(_redController.text),
        int.parse(_greenController.text),
        int.parse(_blueController.text),
      ];

      final success = await ApiService.createScene(
        name: _nameController.text,
        entityId: _entityIdController.text,
        brightness: int.parse(_brightnessController.text),
        rgbColor: rgbColor,
        state: _selectedState,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scene created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create scene'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: Invalid input values'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isCreating = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Create Scene'), backgroundColor: Colors.blue),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Scene Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a scene name';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _entityIdController,
              decoration: InputDecoration(
                labelText: 'Entity ID',
                hintText: 'e.g., light.essentials_lightstrip',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.device_hub),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an entity ID';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _brightnessController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Brightness (0-255)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.brightness_6),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter brightness value';
                }
                final brightness = int.tryParse(value);
                if (brightness == null || brightness < 0 || brightness > 255) {
                  return 'Brightness must be between 0-255';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            Text(
              'RGB Color',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _redController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Red (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Required';
                      final val = int.tryParse(value);
                      if (val == null || val < 0 || val > 255) return '0-255';
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _greenController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Green (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Required';
                      final val = int.tryParse(value);
                      if (val == null || val < 0 || val > 255) return '0-255';
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _blueController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Blue (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Required';
                      final val = int.tryParse(value);
                      if (val == null || val < 0 || val > 255) return '0-255';
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedState,
              decoration: InputDecoration(
                labelText: 'State',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.power_settings_new),
              ),
              items:
                  ['on', 'off'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value.toUpperCase()),
                    );
                  }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedState = newValue!;
                });
              },
            ),
            SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isCreating ? null : _createScene,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child:
                  _isCreating
                      ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Creating...',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      )
                      : Text(
                        'Create Scene',
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}

class SceneDetailPage extends StatelessWidget {
  final Scene scene;

  const SceneDetailPage({super.key, required this.scene});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(scene.name), backgroundColor: Colors.blue),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Scene Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildInfoRow('ID', scene.id),
                  _buildInfoRow('Name', scene.name),
                  _buildInfoRow('Icon', scene.icon),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.device_hub, color: Colors.green),
                      SizedBox(width: 8),
                      Text(
                        'Entities (${scene.entities.length})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  ...scene.entities.entries.map((entity) {
                    return _buildEntityCard(entity.key, entity.value);
                  }).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Widget _buildEntityCard(String entityId, Map<String, dynamic> entityData) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            entityId,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildEntityProperty(
                'State',
                entityData['state']?.toString() ?? 'N/A',
              ),
              _buildEntityProperty(
                'Brightness',
                entityData['brightness']?.toString() ?? 'N/A',
              ),
              if (entityData['rgb_color'] != null)
                _buildEntityProperty(
                  'RGB Color',
                  '${entityData['rgb_color'][0]}, ${entityData['rgb_color'][1]}, ${entityData['rgb_color'][2]}',
                ),
              _buildEntityProperty(
                'Friendly Name',
                entityData['friendly_name']?.toString() ?? 'N/A',
              ),
            ],
          ),
          if (entityData['rgb_color'] != null) ...[
            SizedBox(height: 8),
            Row(
              children: [
                Text('Color Preview: '),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(
                      entityData['rgb_color'][0] ?? 0,
                      entityData['rgb_color'][1] ?? 0,
                      entityData['rgb_color'][2] ?? 0,
                      1.0,
                    ),
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEntityProperty(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(value, style: TextStyle(fontSize: 14)),
      ],
    );
  }
}
