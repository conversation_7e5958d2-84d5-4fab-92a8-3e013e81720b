import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class Scene {
  final String id;
  final String name;
  final Map<String, dynamic> entities;
  final String icon;
  final Map<String, dynamic> metadata;

  Scene({
    required this.id,
    required this.name,
    required this.entities,
    required this.icon,
    required this.metadata,
  });

  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      entities: json['entities'] ?? {},
      icon: json['icon'] ?? 'mdi:account',
      metadata: json['metadata'] ?? {},
    );
  }
}

class EntityInfo {
  final String entityId;
  final String friendlyName;
  final String state;
  final Map<String, dynamic> attributes;
  final String domain;

  EntityInfo({
    required this.entityId,
    required this.friendlyName,
    required this.state,
    required this.attributes,
    required this.domain,
  });

  factory EntityInfo.fromJson(Map<String, dynamic> json) {
    return EntityInfo(
      entityId: json['entity_id'] ?? '',
      friendlyName:
          json['attributes']?['friendly_name'] ?? json['entity_id'] ?? '',
      state: json['state'] ?? '',
      attributes: json['attributes'] ?? {},
      domain: json['entity_id']?.split('.').first ?? '',
    );
  }
}

class ApiService {
  static const String baseUrl = 'http://192.168.6.166:8123/api';
  static const String sceneConfigUrl = '$baseUrl/config/scene/config';
  static const String statesUrl = '$baseUrl/states';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  // Fetch all entities from Home Assistant
  static Future<List<EntityInfo>> getAllEntities() async {
    try {
      final response = await http.get(Uri.parse(statesUrl), headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((entity) => EntityInfo.fromJson(entity)).toList();
      }
      return [];
    } catch (e) {
      log('Error fetching entities: $e');
      return [];
    }
  }

  static Future<bool> createScene({
    required String name,
    required String entityId,
    required Map<String, dynamic> entityState,
  }) async {
    try {
      final sceneId = DateTime.now().millisecondsSinceEpoch.toString();
      final url = '$sceneConfigUrl/$sceneId';

      final body = {
        "name": name,
        "entities": {entityId: entityState},
        "icon": "mdi:palette",
        "metadata": {},
      };

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(body),
      );

      return response.statusCode == 200;
    } catch (e) {
      log('Error creating scene: $e');
      return false;
    }
  }

  static Future<Scene?> getScene(String sceneId) async {
    try {
      final url = '$sceneConfigUrl/$sceneId';
      final response = await http.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Scene.fromJson(data);
      }
      return null;
    } catch (e) {
      log('Error fetching scene: $e');
      return null;
    }
  }

  static Future<List<Scene>> getAllScenes() async {
    // Since Home Assistant doesn't provide a list all scenes endpoint in your example,
    // we'll simulate it by trying to fetch known scene IDs
    // In a real app, you might want to store scene IDs locally or use a different API endpoint
    List<Scene> scenes = [];

    // This is a simulation - in reality you'd get this list from HA or store locally
    List<String> knownSceneIds = [
      '1750054948098',
    ]; // Add your known scene IDs here

    for (String id in knownSceneIds) {
      Scene? scene = await getScene(id);
      if (scene != null) {
        scenes.add(scene);
      }
    }

    return scenes;
  }
}

class SceneListPage extends StatefulWidget {
  const SceneListPage({super.key});

  @override
  SceneListPageState createState() => SceneListPageState();
}

class SceneListPageState extends State<SceneListPage> {
  List<Scene> scenes = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadScenes();
  }

  Future<void> loadScenes() async {
    setState(() => isLoading = true);
    scenes = await ApiService.getAllScenes();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Scenes'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body:
          isLoading
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : RefreshIndicator(
                onRefresh: loadScenes,
                color: Colors.blue,
                child:
                    scenes.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.palette, size: 64, color: Colors.blue),
                              SizedBox(height: 16),
                              Text(
                                'No scenes found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Pull to refresh or create a new scene',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: scenes.length,
                          itemBuilder: (context, index) {
                            final scene = scenes[index];
                            return Card(
                              margin: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.blue,
                                  child: Icon(
                                    Icons.palette,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(
                                  scene.name,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Text(
                                  '${scene.entities.length} entities',
                                  style: TextStyle(
                                    color: Colors.blue.withValues(alpha: 0.7),
                                  ),
                                ),
                                trailing: Icon(
                                  Icons.arrow_forward_ios,
                                  color: Colors.blue,
                                ),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              SceneDetailPage(scene: scene),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => CreateScenePage()),
          );
          if (result == true) {
            loadScenes();
          }
        },
        backgroundColor: Colors.blue,
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class CreateScenePage extends StatefulWidget {
  const CreateScenePage({super.key});

  @override
  CreateScenePageState createState() => CreateScenePageState();
}

class CreateScenePageState extends State<CreateScenePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  List<EntityInfo> _entities = [];
  EntityInfo? _selectedEntity;
  Map<String, dynamic> _entityState = {};
  bool _isLoadingEntities = true;
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    _loadEntities();
  }

  Future<void> _loadEntities() async {
    setState(() => _isLoadingEntities = true);
    final entities = await ApiService.getAllEntities();
    setState(() {
      _entities =
          entities
              .where(
                (entity) =>
                    entity.domain == 'light' ||
                    entity.domain == 'fan' ||
                    entity.domain == 'switch',
              )
              .toList();
      _isLoadingEntities = false;
    });
  }

  void _onEntitySelected(EntityInfo? entity) {
    setState(() {
      _selectedEntity = entity;
      if (entity != null) {
        _entityState = _buildDefaultEntityState(entity);
      } else {
        _entityState = {};
      }
    });
  }

  Map<String, dynamic> _buildDefaultEntityState(EntityInfo entity) {
    final state = <String, dynamic>{
      'state': entity.state,
      'friendly_name': entity.friendlyName,
    };

    // Add domain-specific attributes
    switch (entity.domain) {
      case 'light':
        state['brightness'] = entity.attributes['brightness'] ?? 255;
        if (entity.attributes['rgb_color'] != null) {
          state['rgb_color'] = entity.attributes['rgb_color'];
        }
        if (entity.attributes['supported_color_modes'] != null) {
          state['supported_color_modes'] =
              entity.attributes['supported_color_modes'];
        }
        break;
      case 'fan':
        if (entity.attributes['preset_modes'] != null) {
          state['preset_mode'] = entity.attributes['preset_mode'] ?? 'low';
          state['preset_modes'] = entity.attributes['preset_modes'];
        }
        break;
      case 'switch':
        // Switches typically only have state
        break;
    }

    return state;
  }

  Widget _buildEntityControls() {
    if (_selectedEntity == null) return SizedBox.shrink();

    final entity = _selectedEntity!;
    final controls = <Widget>[];

    // State control (common for all entities)
    controls.add(
      DropdownButtonFormField<String>(
        value: _entityState['state'] ?? entity.state,
        decoration: InputDecoration(
          labelText: 'State',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.power_settings_new, color: Colors.blue),
        ),
        items:
            ['on', 'off'].map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value.toUpperCase()),
              );
            }).toList(),
        onChanged: (String? newValue) {
          setState(() {
            _entityState['state'] = newValue!;
          });
        },
      ),
    );

    // Domain-specific controls
    switch (entity.domain) {
      case 'light':
        controls.add(SizedBox(height: 16));

        // Brightness control
        controls.add(
          TextFormField(
            initialValue: _entityState['brightness']?.toString() ?? '255',
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Brightness (0-255)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.brightness_6, color: Colors.blue),
            ),
            onChanged: (value) {
              final brightness = int.tryParse(value);
              if (brightness != null) {
                setState(() {
                  _entityState['brightness'] = brightness;
                });
              }
            },
          ),
        );

        // RGB Color control (if supported)
        if (entity.attributes['supported_color_modes']?.contains('rgb') ==
            true) {
          controls.add(SizedBox(height: 16));
          controls.add(
            Text(
              'RGB Color',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          );
          controls.add(SizedBox(height: 8));

          final currentRgb = _entityState['rgb_color'] ?? [255, 255, 255];
          controls.add(
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[0].toString(),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Red (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final red = int.tryParse(value);
                      if (red != null && red >= 0 && red <= 255) {
                        setState(() {
                          _entityState['rgb_color'] = [
                            red,
                            currentRgb[1],
                            currentRgb[2],
                          ];
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[1].toString(),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Green (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final green = int.tryParse(value);
                      if (green != null && green >= 0 && green <= 255) {
                        setState(() {
                          _entityState['rgb_color'] = [
                            currentRgb[0],
                            green,
                            currentRgb[2],
                          ];
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[2].toString(),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Blue (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final blue = int.tryParse(value);
                      if (blue != null && blue >= 0 && blue <= 255) {
                        setState(() {
                          _entityState['rgb_color'] = [
                            currentRgb[0],
                            currentRgb[1],
                            blue,
                          ];
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        }
        break;

      case 'fan':
        if (entity.attributes['preset_modes'] != null) {
          controls.add(SizedBox(height: 16));
          controls.add(
            DropdownButtonFormField<String>(
              value:
                  _entityState['preset_mode'] ??
                  entity.attributes['preset_mode'],
              decoration: InputDecoration(
                labelText: 'Preset Mode',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.air, color: Colors.blue),
              ),
              items:
                  (entity.attributes['preset_modes'] as List<dynamic>).map((
                    mode,
                  ) {
                    return DropdownMenuItem<String>(
                      value: mode.toString(),
                      child: Text(mode.toString().toUpperCase()),
                    );
                  }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _entityState['preset_mode'] = newValue!;
                });
              },
            ),
          );
        }
        break;

      case 'switch':
        // Switches typically only have state control
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: controls,
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _createScene() async {
    if (!_formKey.currentState!.validate() || _selectedEntity == null) return;

    setState(() => _isCreating = true);

    try {
      final success = await ApiService.createScene(
        name: _nameController.text,
        entityId: _selectedEntity!.entityId,
        entityState: _entityState,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Scene created successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to create scene'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create Scene'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoadingEntities
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : Form(
                key: _formKey,
                child: ListView(
                  padding: EdgeInsets.all(16),
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: 'Scene Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.palette, color: Colors.blue),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a scene name';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16),
                    DropdownButtonFormField<EntityInfo>(
                      value: _selectedEntity,
                      decoration: InputDecoration(
                        labelText: 'Select Entity',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.device_hub, color: Colors.blue),
                      ),
                      items:
                          _entities.map((entity) {
                            return DropdownMenuItem(
                              value: entity,
                              child: Text(
                                '${entity.friendlyName} (${entity.entityId})',
                              ),
                            );
                          }).toList(),
                      onChanged: _onEntitySelected,
                      validator: (value) {
                        if (value == null) {
                          return 'Please select an entity';
                        }
                        return null;
                      },
                    ),
                    if (_selectedEntity != null) ...[
                      SizedBox(height: 16),
                      Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Entity Configuration',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: 16),
                              _buildEntityControls(),
                            ],
                          ),
                        ),
                      ),
                    ],
                    SizedBox(height: 32),
                    ElevatedButton(
                      onPressed: _isCreating ? null : _createScene,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                      child:
                          _isCreating
                              ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Creating...',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              )
                              : Text(
                                'Create Scene',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ],
                ),
              ),
    );
  }
}

class SceneDetailPage extends StatelessWidget {
  final Scene scene;

  const SceneDetailPage({super.key, required this.scene});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(scene.name),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Scene Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildInfoRow('ID', scene.id),
                  _buildInfoRow('Name', scene.name),
                  _buildInfoRow('Icon', scene.icon),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.device_hub, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Entities (${scene.entities.length})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  ...scene.entities.entries.map((entity) {
                    return _buildEntityCard(entity.key, entity.value);
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Widget _buildEntityCard(String entityId, Map<String, dynamic> entityData) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            entityId,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildEntityProperty(
                'State',
                entityData['state']?.toString() ?? 'N/A',
              ),
              _buildEntityProperty(
                'Brightness',
                entityData['brightness']?.toString() ?? 'N/A',
              ),
              if (entityData['rgb_color'] != null)
                _buildEntityProperty(
                  'RGB Color',
                  '${entityData['rgb_color'][0]}, ${entityData['rgb_color'][1]}, ${entityData['rgb_color'][2]}',
                ),
              _buildEntityProperty(
                'Friendly Name',
                entityData['friendly_name']?.toString() ?? 'N/A',
              ),
            ],
          ),
          if (entityData['rgb_color'] != null) ...[
            SizedBox(height: 8),
            Row(
              children: [
                Text('Color Preview: '),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(
                      entityData['rgb_color'][0] ?? 0,
                      entityData['rgb_color'][1] ?? 0,
                      entityData['rgb_color'][2] ?? 0,
                      1.0,
                    ),
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEntityProperty(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(value, style: TextStyle(fontSize: 14)),
      ],
    );
  }
}
