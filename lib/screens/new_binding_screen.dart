import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/new_binding_api_service.dart';
import '../services/binding_storage_service.dart';

class NewBindingScreen extends StatefulWidget {
  const NewBindingScreen({super.key});

  @override
  State<NewBindingScreen> createState() => _NewBindingScreenState();
}

class _NewBindingScreenState extends State<NewBindingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final NewBindingApiService _apiService = NewBindingApiService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Matter Device Binding'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.devices), text: 'Devices'),
            Tab(icon: Icon(Icons.link), text: 'Create Binding'),
            Tab(icon: Icon(Icons.list), text: 'Bindings'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          DevicesTab(apiService: _apiService),
          CreateBindingTab(apiService: _apiService),
          BindingsTab(apiService: _apiService),
        ],
      ),
    );
  }
}

class DevicesTab extends StatefulWidget {
  final NewBindingApiService apiService;

  const DevicesTab({super.key, required this.apiService});

  @override
  State<DevicesTab> createState() => _DevicesTabState();
}

class _DevicesTabState extends State<DevicesTab> {
  List<PairedDevice> _devices = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDevices();
  }

  Future<void> _loadDevices() async {
    setState(() => _isLoading = true);
    final devices = await widget.apiService.getAllPairedDevices();
    setState(() {
      _devices = devices;
      _isLoading = false;
    });
  }

  Future<void> _showPairDeviceDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => PairDeviceDialog(apiService: widget.apiService),
    );

    if (result == true) {
      _loadDevices();
    }
  }

  Future<void> _removeDevice(PairedDevice device) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Remove Device'),
            content: Text(
              'Are you sure you want to remove "${device.name}"?\n\nThis will also remove any bindings involving this device.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: Text('Remove'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await widget.apiService.removePairedDevice(device.nodeId);
      if (success) {
        _loadDevices();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Device "${device.name}" removed successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : RefreshIndicator(
                onRefresh: _loadDevices,
                color: Colors.blue,
                child:
                    _devices.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.devices, size: 64, color: Colors.blue),
                              SizedBox(height: 16),
                              Text(
                                'No paired devices',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Tap the + button to pair a new device',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          padding: EdgeInsets.all(16),
                          itemCount: _devices.length,
                          itemBuilder: (context, index) {
                            final device = _devices[index];
                            return Card(
                              margin: EdgeInsets.only(bottom: 12),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.blue,
                                  child: Text(
                                    device.nodeId.toString(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  device.name,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Type: ${device.deviceType}'),
                                    Text('Node ID: ${device.nodeId}'),
                                    Text(
                                      'Endpoints: ${device.endpoints.join(', ')}',
                                    ),
                                    Text(
                                      'Paired: ${_formatDate(device.pairedAt)}',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                  ],
                                ),
                                trailing: IconButton(
                                  icon: Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _removeDevice(device),
                                ),
                                isThreeLine: true,
                              ),
                            );
                          },
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showPairDeviceDialog,
        backgroundColor: Colors.blue,
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}

class PairDeviceDialog extends StatefulWidget {
  final NewBindingApiService apiService;

  const PairDeviceDialog({super.key, required this.apiService});

  @override
  State<PairDeviceDialog> createState() => _PairDeviceDialogState();
}

class _PairDeviceDialogState extends State<PairDeviceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _passcodeController = TextEditingController();
  String _selectedDeviceType = 'Switch';
  bool _isPairing = false;

  @override
  void dispose() {
    _nameController.dispose();
    _passcodeController.dispose();
    super.dispose();
  }

  Future<void> _pairDevice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isPairing = true);

    try {
      final device = await widget.apiService.pairDevice(
        deviceName: _nameController.text,
        deviceType: _selectedDeviceType,
        passcode: _passcodeController.text,
      );

      if (mounted) {
        if (device != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Device "${device.name}" paired successfully with Node ID ${device.nodeId}',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to pair device. Please check the passcode and try again.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isPairing = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Pair New Device'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Device Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.device_hub, color: Colors.blue),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a device name';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedDeviceType,
              decoration: InputDecoration(
                labelText: 'Device Type',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category, color: Colors.blue),
              ),
              items:
                  widget.apiService.getDeviceTypes().map((type) {
                    return DropdownMenuItem(value: type, child: Text(type));
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedDeviceType = value!;
                });
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _passcodeController,
              decoration: InputDecoration(
                labelText: 'Share Code (Passcode)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.key, color: Colors.blue),
                hintText: 'e.g., 12988108191',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the share code';
                }
                if (value.length < 8) {
                  return 'Share code must be at least 8 digits';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isPairing ? null : () => Navigator.pop(context, false),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isPairing ? null : _pairDevice,
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
          child:
              _isPairing
                  ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : Text('Pair Device', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}

class CreateBindingTab extends StatefulWidget {
  final NewBindingApiService apiService;

  const CreateBindingTab({super.key, required this.apiService});

  @override
  State<CreateBindingTab> createState() => _CreateBindingTabState();
}

class _CreateBindingTabState extends State<CreateBindingTab> {
  final _formKey = GlobalKey<FormState>();
  final _sourceNodeController = TextEditingController();
  final _targetNodeController = TextEditingController();

  List<PairedDevice> _devices = [];
  int _sourceEndpoint = 1;
  int _targetEndpoint = 1;
  bool _isCreating = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDevices();
  }

  Future<void> _loadDevices() async {
    setState(() => _isLoading = true);
    final devices = await widget.apiService.getAllPairedDevices();
    setState(() {
      _devices = devices;
      _isLoading = false;
    });
  }

  Future<void> _createBinding() async {
    if (!_formKey.currentState!.validate()) return;

    final sourceNodeId = int.tryParse(_sourceNodeController.text);
    final targetNodeId = int.tryParse(_targetNodeController.text);

    if (sourceNodeId == null || targetNodeId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please enter valid node IDs'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!widget.apiService.validateBinding(
      sourceNodeId: sourceNodeId,
      sourceEndpoint: _sourceEndpoint,
      targetNodeId: targetNodeId,
      targetEndpoint: _targetEndpoint,
    )) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invalid binding configuration'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isCreating = true);

    try {
      final binding = await widget.apiService.createBinding(
        sourceNodeId: sourceNodeId,
        sourceEndpoint: _sourceEndpoint,
        targetNodeId: targetNodeId,
        targetEndpoint: _targetEndpoint,
      );

      if (mounted) {
        if (binding != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Binding created successfully: ${binding.sourceName} → ${binding.targetName}',
              ),
              backgroundColor: Colors.green,
            ),
          );

          // Clear form
          _sourceNodeController.clear();
          _targetNodeController.clear();
          setState(() {
            _sourceEndpoint = 1;
            _targetEndpoint = 1;
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to create binding. Please check the configuration.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  void dispose() {
    _sourceNodeController.dispose();
    _targetNodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16),
      child:
          _isLoading
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : Form(
                key: _formKey,
                child: ListView(
                  children: [
                    Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Available Devices',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            SizedBox(height: 16),
                            if (_devices.isEmpty)
                              Text(
                                'No paired devices available. Please pair devices first.',
                                style: TextStyle(
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              )
                            else
                              ...(_devices.map(
                                (device) => Padding(
                                  padding: EdgeInsets.only(bottom: 8),
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        radius: 16,
                                        backgroundColor: Colors.blue,
                                        child: Text(
                                          device.nodeId.toString(),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Expanded(
                                        child: Text(
                                          '${device.name} (${device.deviceType})',
                                          style: TextStyle(fontSize: 14),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Create New Binding',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _sourceNodeController,
                                    decoration: InputDecoration(
                                      labelText: 'Source Node ID',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(
                                        Icons.input,
                                        color: Colors.blue,
                                      ),
                                      hintText: 'e.g., 1',
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Required';
                                      }
                                      final nodeId = int.tryParse(value);
                                      if (nodeId == null || nodeId <= 0) {
                                        return 'Invalid node ID';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                SizedBox(width: 16),
                                Expanded(
                                  child: DropdownButtonFormField<int>(
                                    value: _sourceEndpoint,
                                    decoration: InputDecoration(
                                      labelText: 'Source Endpoint',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(
                                        Icons.settings_ethernet,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    items:
                                        [1, 2, 3, 4, 5].map((endpoint) {
                                          return DropdownMenuItem(
                                            value: endpoint,
                                            child: Text(endpoint.toString()),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _sourceEndpoint = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _targetNodeController,
                                    decoration: InputDecoration(
                                      labelText: 'Target Node ID',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(
                                        Icons.output,
                                        color: Colors.blue,
                                      ),
                                      hintText: 'e.g., 2',
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Required';
                                      }
                                      final nodeId = int.tryParse(value);
                                      if (nodeId == null || nodeId <= 0) {
                                        return 'Invalid node ID';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                SizedBox(width: 16),
                                Expanded(
                                  child: DropdownButtonFormField<int>(
                                    value: _targetEndpoint,
                                    decoration: InputDecoration(
                                      labelText: 'Target Endpoint',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(
                                        Icons.settings_ethernet,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    items:
                                        [1, 2, 3, 4, 5].map((endpoint) {
                                          return DropdownMenuItem(
                                            value: endpoint,
                                            child: Text(endpoint.toString()),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _targetEndpoint = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isCreating ? null : _createBinding,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                ),
                                child:
                                    _isCreating
                                        ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                      Color
                                                    >(Colors.white),
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              'Creating...',
                                              style: TextStyle(
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        )
                                        : Text(
                                          'Create Binding',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.white,
                                          ),
                                        ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}

class BindingsTab extends StatefulWidget {
  final NewBindingApiService apiService;

  const BindingsTab({super.key, required this.apiService});

  @override
  State<BindingsTab> createState() => _BindingsTabState();
}

class _BindingsTabState extends State<BindingsTab> {
  List<DeviceBinding> _bindings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBindings();
  }

  Future<void> _loadBindings() async {
    setState(() => _isLoading = true);
    final bindings = await widget.apiService.getAllBindings();
    setState(() {
      _bindings = bindings;
      _isLoading = false;
    });
  }

  Future<void> _removeBinding(DeviceBinding binding) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Remove Binding'),
            content: Text(
              'Are you sure you want to remove the binding between "${binding.sourceName}" and "${binding.targetName}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: Text('Remove'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await widget.apiService.removeBinding(binding.id);
      if (success) {
        _loadBindings();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Binding removed successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? Center(child: CircularProgressIndicator(color: Colors.blue))
        : RefreshIndicator(
          onRefresh: _loadBindings,
          color: Colors.blue,
          child:
              _bindings.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.link_off, size: 64, color: Colors.blue),
                        SizedBox(height: 16),
                        Text(
                          'No bindings found',
                          style: TextStyle(fontSize: 18, color: Colors.blue),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Create bindings in the "Create Binding" tab',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: EdgeInsets.all(16),
                    itemCount: _bindings.length,
                    itemBuilder: (context, index) {
                      final binding = _bindings[index];
                      return Card(
                        margin: EdgeInsets.only(bottom: 12),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      '${binding.sourceName} → ${binding.targetName}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _removeBinding(binding),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Row(
                                children: [
                                  _buildNodeInfo(
                                    'Source',
                                    binding.sourceNodeId,
                                    binding.sourceEndpoint,
                                    Colors.blue,
                                  ),
                                  SizedBox(width: 16),
                                  Icon(Icons.arrow_forward, color: Colors.blue),
                                  SizedBox(width: 16),
                                  _buildNodeInfo(
                                    'Target',
                                    binding.targetNodeId,
                                    binding.targetEndpoint,
                                    Colors.green,
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Created: ${_formatDate(binding.createdAt)}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
        );
  }

  Widget _buildNodeInfo(String label, int nodeId, int endpoint, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            border: Border.all(color: color),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Node $nodeId:$endpoint',
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
