import 'dart:convert';
import 'dart:io';
import 'dart:developer';
import 'package:path_provider/path_provider.dart';

class PairedDevice {
  final String name;
  final String deviceType;
  final int nodeId;
  final String passcode;
  final DateTime pairedAt;
  final List<int> endpoints;

  PairedDevice({
    required this.name,
    required this.deviceType,
    required this.nodeId,
    required this.passcode,
    required this.pairedAt,
    this.endpoints = const [1],
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'deviceType': deviceType,
      'nodeId': nodeId,
      'passcode': passcode,
      'pairedAt': pairedAt.toIso8601String(),
      'endpoints': endpoints,
    };
  }

  factory PairedDevice.fromJson(Map<String, dynamic> json) {
    return PairedDevice(
      name: json['name'] ?? '',
      deviceType: json['deviceType'] ?? '',
      nodeId: json['nodeId'] ?? 0,
      passcode: json['passcode'] ?? '',
      pairedAt: DateTime.parse(json['pairedAt'] ?? DateTime.now().toIso8601String()),
      endpoints: List<int>.from(json['endpoints'] ?? [1]),
    );
  }
}

class DeviceBinding {
  final String id;
  final int sourceNodeId;
  final int sourceEndpoint;
  final int targetNodeId;
  final int targetEndpoint;
  final String sourceName;
  final String targetName;
  final DateTime createdAt;

  DeviceBinding({
    required this.id,
    required this.sourceNodeId,
    required this.sourceEndpoint,
    required this.targetNodeId,
    required this.targetEndpoint,
    required this.sourceName,
    required this.targetName,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sourceNodeId': sourceNodeId,
      'sourceEndpoint': sourceEndpoint,
      'targetNodeId': targetNodeId,
      'targetEndpoint': targetEndpoint,
      'sourceName': sourceName,
      'targetName': targetName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory DeviceBinding.fromJson(Map<String, dynamic> json) {
    return DeviceBinding(
      id: json['id'] ?? '',
      sourceNodeId: json['sourceNodeId'] ?? 0,
      sourceEndpoint: json['sourceEndpoint'] ?? 1,
      targetNodeId: json['targetNodeId'] ?? 0,
      targetEndpoint: json['targetEndpoint'] ?? 1,
      sourceName: json['sourceName'] ?? '',
      targetName: json['targetName'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class BindingStorageService {
  static const String _devicesFileName = 'paired_devices.json';
  static const String _bindingsFileName = 'device_bindings.json';

  // Get the application documents directory
  Future<String> get _localPath async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  // Get the devices file
  Future<File> get _devicesFile async {
    final path = await _localPath;
    return File('$path/$_devicesFileName');
  }

  // Get the bindings file
  Future<File> get _bindingsFile async {
    final path = await _localPath;
    return File('$path/$_bindingsFileName');
  }

  // Load paired devices from JSON file
  Future<List<PairedDevice>> loadPairedDevices() async {
    try {
      final file = await _devicesFile;
      if (!await file.exists()) {
        return [];
      }

      final contents = await file.readAsString();
      final List<dynamic> jsonList = json.decode(contents);
      return jsonList.map((json) => PairedDevice.fromJson(json)).toList();
    } catch (e) {
      log('Error loading paired devices: $e');
      return [];
    }
  }

  // Save paired devices to JSON file
  Future<void> savePairedDevices(List<PairedDevice> devices) async {
    try {
      final file = await _devicesFile;
      final jsonList = devices.map((device) => device.toJson()).toList();
      await file.writeAsString(json.encode(jsonList));
    } catch (e) {
      log('Error saving paired devices: $e');
    }
  }

  // Add a new paired device
  Future<void> addPairedDevice(PairedDevice device) async {
    final devices = await loadPairedDevices();
    
    // Remove existing device with same node ID if exists
    devices.removeWhere((d) => d.nodeId == device.nodeId);
    
    devices.add(device);
    await savePairedDevices(devices);
  }

  // Remove a paired device
  Future<void> removePairedDevice(int nodeId) async {
    final devices = await loadPairedDevices();
    devices.removeWhere((d) => d.nodeId == nodeId);
    await savePairedDevices(devices);
  }

  // Load device bindings from JSON file
  Future<List<DeviceBinding>> loadDeviceBindings() async {
    try {
      final file = await _bindingsFile;
      if (!await file.exists()) {
        return [];
      }

      final contents = await file.readAsString();
      final List<dynamic> jsonList = json.decode(contents);
      return jsonList.map((json) => DeviceBinding.fromJson(json)).toList();
    } catch (e) {
      log('Error loading device bindings: $e');
      return [];
    }
  }

  // Save device bindings to JSON file
  Future<void> saveDeviceBindings(List<DeviceBinding> bindings) async {
    try {
      final file = await _bindingsFile;
      final jsonList = bindings.map((binding) => binding.toJson()).toList();
      await file.writeAsString(json.encode(jsonList));
    } catch (e) {
      log('Error saving device bindings: $e');
    }
  }

  // Add a new device binding
  Future<void> addDeviceBinding(DeviceBinding binding) async {
    final bindings = await loadDeviceBindings();
    bindings.add(binding);
    await saveDeviceBindings(bindings);
  }

  // Remove a device binding
  Future<void> removeDeviceBinding(String bindingId) async {
    final bindings = await loadDeviceBindings();
    bindings.removeWhere((b) => b.id == bindingId);
    await saveDeviceBindings(bindings);
  }

  // Get next available node ID
  Future<int> getNextNodeId() async {
    final devices = await loadPairedDevices();
    if (devices.isEmpty) {
      return 1;
    }
    
    final maxNodeId = devices.map((d) => d.nodeId).reduce((a, b) => a > b ? a : b);
    return maxNodeId + 1;
  }

  // Check if node ID is already used
  Future<bool> isNodeIdUsed(int nodeId) async {
    final devices = await loadPairedDevices();
    return devices.any((d) => d.nodeId == nodeId);
  }

  // Get device by node ID
  Future<PairedDevice?> getDeviceByNodeId(int nodeId) async {
    final devices = await loadPairedDevices();
    try {
      return devices.firstWhere((d) => d.nodeId == nodeId);
    } catch (e) {
      return null;
    }
  }

  // Clear all data (for testing purposes)
  Future<void> clearAllData() async {
    try {
      final devicesFile = await _devicesFile;
      final bindingsFile = await _bindingsFile;
      
      if (await devicesFile.exists()) {
        await devicesFile.delete();
      }
      
      if (await bindingsFile.exists()) {
        await bindingsFile.delete();
      }
    } catch (e) {
      log('Error clearing data: $e');
    }
  }
}
